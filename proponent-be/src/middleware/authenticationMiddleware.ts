import { Request, Response, NextFunction } from 'express';
import { getSupabaseClientWithUserSession } from '@/db/supabaseClient';
import { getUser } from '@/service/db-calls/getUser';
import { UserMetadata } from '@/types/express';
import { AuthError } from '@supabase/supabase-js';
import { getUserData } from '@/service/user/db/getUserData';

export const authenticateRequest = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) return res.status(401).json({ error: 'Unauthorized login to supabase auth' });

    const supabase = getSupabaseClientWithUserSession(authHeader);

    const { user } = await getUser(supabase);
    if (!user || !user.email || !user.id) return res.status(401).json({ error: 'User not found, try logging in again' });

    let tenantUser = await getUserData(user.id);
    
    const userMetadata: UserMetadata = {
      email: user.email,
      userId: user.id,
      firstName: user.user_metadata.firstName,
      lastName: user.user_metadata.lastName,
      tenantName: user.user_metadata.tenantName,
      tenantSize: user.user_metadata.tenantSize,
    };

    req.user = {
      role: user.user_metadata.role, // Storing role here
      tenantId: user.user_metadata.tenantId,
      supabase: supabase,
      userId: user.id,
    };

    res.locals.userMetadata = userMetadata;

    return next();
  } catch (error: unknown) {
    console.log('Error from supabase auth middleware', error);
    if (error instanceof AuthError && error.status === 401) return res.status(401).json({ error: 'Unauthorized: session not found' });

    if (typeof error === 'object' && error !== null && '__isAuthError' in error && error.__isAuthError === true) return res.status(401).json({ error: 'Unauthorized: session not found' });

    // Log security-related errors
    console.error('Security Error:', {
      timestamp: new Date().toISOString(),
      ip: req.ip,
      path: req.path,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    throw error;
  }
};
