import { supabaseAdminClient } from '@/db/supabaseClient';
import { DatabaseResponseError } from '@/errors/databaseResponseError';

export const getUserData = async (userId: string): Promise<{ email: string; tenantId: number }> => {
  const { data: userEmailData, error: userEmailError } = await supabaseAdminClient.from('users')
  .select('email,tenantId: tenant_id, firstName:first_name, lastName:last_name, email: email')
  .eq('user_id', userId).single();
  if (userEmailError || !userEmailData) throw new DatabaseResponseError('Error fetching user email', userEmailError);

  return userEmailData;
};
